const fs = require('fs');
const path = require('path');

// 读取最新的 AI 日志文件
const logDir = '/Users/<USER>/works/galaxy/vue3-element-plus/ai-logs';
const logFiles = fs.readdirSync(logDir)
  .filter(file => file.includes('runtime-error-fix'))
  .sort()
  .reverse();

if (logFiles.length === 0) {
  console.log('没有找到运行时错误修复日志文件');
  process.exit(1);
}

const latestLogFile = path.join(logDir, logFiles[0]);
const logData = JSON.parse(fs.readFileSync(latestLogFile, 'utf8'));
const response = logData.fullResponse;

console.log('最新日志文件:', logFiles[0]);
console.log('响应长度:', response.length);

// 解码 HTML 实体
function decodeHtmlEntities(text) {
  const entities = {
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&quot;': '"',
    '&#39;': "'",
    '&apos;': "'"
  };
  
  return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
    return entities[entity] || entity;
  });
}

console.log('\n检查响应格式:');
console.log('包含<fix_result>:', response.includes('<fix_result>'));
console.log('包含</fix_result>:', response.includes('</fix_result>'));
console.log('包含<fixed_content>:', response.includes('<fixed_content>'));
console.log('包含</fixed_content>:', response.includes('</fixed_content>'));

// 尝试解析运行时错误修复的特殊格式
const runtimeFixMatch = response.match(/<fix_result>[\s\S]*?<fixed_content>([\s\S]*?)<\/fixed_content>[\s\S]*?<\/fix_result>/);

if (runtimeFixMatch) {
  let content = runtimeFixMatch[1].trim();
  
  console.log('\n原始内容前200字符:');
  console.log(content.substring(0, 200));
  
  // 解码 HTML 实体
  content = decodeHtmlEntities(content);
  
  console.log('\n解码后内容前200字符:');
  console.log(content.substring(0, 200));
  
  console.log('\n包含<template>:', content.includes('<template>'));
  console.log('包含<script>:', content.includes('<script'));
  console.log('包含<script setup>:', content.includes('<script setup>'));
  console.log('包含<style>:', content.includes('<style>'));
  
  console.log('\n完整解码后内容:');
  console.log(content);
} else {
  console.log('\n❌ 没有匹配到运行时修复格式');
  
  // 尝试其他格式
  const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);
  if (xmlMatch) {
    console.log('✅ 匹配到标准 XML 格式');
  } else {
    console.log('❌ 也没有匹配到标准 XML 格式');
  }
}
